#!/usr/bin/env python3
"""
Policy Gradient module
"""

import numpy as np


def policy(matrix, weight):
    """
    Computes the policy with a weight of a matrix.
    
    Args:
        matrix: numpy.ndarray of shape (m, n) representing the state matrix
        weight: numpy.ndarray of shape (n, k) representing the weight matrix
    
    Returns:
        numpy.ndarray of shape (m, k) representing the policy probabilities
    """
    # Compute the dot product of matrix and weight
    z = np.dot(matrix, weight)
    
    # Apply softmax to get probabilities
    # Subtract max for numerical stability
    z_max = np.max(z, axis=1, keepdims=True)
    exp_z = np.exp(z - z_max)
    softmax = exp_z / np.sum(exp_z, axis=1, keepdims=True)
    
    return softmax
