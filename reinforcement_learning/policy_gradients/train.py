#!/usr/bin/env python3
"""
Training module for policy gradient
"""

import numpy as np
policy_gradient = __import__('policy_gradient').policy_gradient


def train(env, nb_episodes, alpha=0.000045, gamma=0.98):
    """
    Implements a full training using policy gradient method.
    
    Args:
        env: initial environment
        nb_episodes: number of episodes used for training
        alpha: the learning rate
        gamma: the discount factor
    
    Returns:
        list: all values of the score (sum of all rewards during one episode loop)
    """
    # Get state and action space dimensions
    state_shape = env.observation_space.shape[0]
    action_shape = env.action_space.n
    
    # Initialize weight matrix with random values
    weight = np.random.rand(state_shape, action_shape)
    
    # List to store scores for each episode
    scores = []
    
    for episode in range(nb_episodes):
        # Reset environment for new episode
        state, _ = env.reset()
        episode_rewards = []
        episode_gradients = []
        
        # Run episode until done
        done = False
        while not done:
            # Get action and gradient using policy_gradient
            action, gradient = policy_gradient(state, weight)
            
            # Take action in environment
            next_state, reward, terminated, truncated, _ = env.step(action)
            done = terminated or truncated
            
            # Store reward and gradient
            episode_rewards.append(reward)
            episode_gradients.append(gradient)
            
            # Update state
            state = next_state
        
        # Calculate score (sum of rewards)
        score = sum(episode_rewards)
        scores.append(score)
        
        # Calculate discounted rewards
        discounted_rewards = []
        running_sum = 0
        for reward in episode_rewards[::-1]:  # Reverse order
            running_sum = reward + gamma * running_sum
            discounted_rewards.append(running_sum)
        discounted_rewards.reverse()
        
        # Normalize discounted rewards
        discounted_rewards = np.array(discounted_rewards)
        discounted_rewards = (discounted_rewards - np.mean(discounted_rewards)) / (np.std(discounted_rewards) + 1e-9)
        
        # Update weights using policy gradient
        for gradient, reward in zip(episode_gradients, discounted_rewards):
            weight += alpha * gradient * reward
        
        # Print episode information
        print(f"Episode: {episode} Score: {score}")
    
    return scores
